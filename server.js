const express = require("express");
const http = require("http");
const WebSocket = require("ws");
const { Server } = require("socket.io");
const axios = require("axios");

const BINANCE_API = "https://api.binance.com/api/v3/exchangeInfo";
const STREAM_URL = "wss://stream.binance.com:9443/stream";

const INTERVAL = "5m";     // candle interval (changeable)
const QUOTE_ASSET = "USDT";
const THRESHOLD = 0.5;     // % threshold (changeable)

// Volume spike multiplier
const VOLUME_MULTIPLIER = 3; // 3x average volume

// RSI settings
const RSI_PERIOD = 14; // Standard RSI period

const app = express();
const server = http.createServer(app);
const io = new Server(server);
app.use(express.static("public"));

// ✅ Binance se trading pairs fetch
async function getSymbols() {
  const res = await fetch(BINANCE_API);
  const data = await res.json();
  return data.symbols
    .filter(
      (s) =>
        s.status === "TRADING" &&
        s.isSpotTradingAllowed &&
        s.symbol.endsWith(QUOTE_ASSET)
    )
    .map((s) => s.symbol.toLowerCase() + "@kline_" + INTERVAL);
}

// ✅ RSI calculation function
function calculateRSI(prices, period = RSI_PERIOD) {
  if (prices.length < period + 1) return null;

  const gains = [];
  const losses = [];

  // Calculate price changes
  for (let i = 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }

  // Calculate initial average gain and loss
  let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;

  // Calculate RSI using Wilder's smoothing method
  for (let i = period; i < gains.length; i++) {
    avgGain = (avgGain * (period - 1) + gains[i]) / period;
    avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
  }

  if (avgLoss === 0) return 100;
  const rs = avgGain / avgLoss;
  const rsi = 100 - (100 / (1 + rs));

  return Math.round(rsi * 100) / 100; // Round to 2 decimal places
}

// ✅ Get RSI for a symbol
async function getRSI(symbol, interval = INTERVAL) {
  try {
    const url = `https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=${interval}&limit=${RSI_PERIOD + 20}`;
    const res = await axios.get(url);
    const candles = res.data;

    // Extract closing prices
    const closePrices = candles.map(candle => parseFloat(candle[4]));

    return calculateRSI(closePrices);
  } catch (err) {
    console.error("RSI error", symbol, err.message);
    return null;
  }
}

// ✅ volume spike check with detailed volume data
async function checkVolumeSpike(symbol, interval = INTERVAL) {
  try {
    const url = `https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=${interval}&limit=20`;
    const res = await axios.get(url);
    const candles = res.data;

    const lastCandle = candles[candles.length - 1];
    const currentVolume = parseFloat(lastCandle[5]);

    const prevVolumes = candles.slice(0, -1).map((c) => parseFloat(c[5]));
    const avgVolume =
      prevVolumes.reduce((a, b) => a + b, 0) / prevVolumes.length;

    const maxVolume = Math.max(...prevVolumes);
    const volumeRatio = currentVolume / avgVolume;

    return {
      isSpike: currentVolume >= VOLUME_MULTIPLIER * avgVolume,
      currentVolume,
      avgVolume,
      maxVolume,
      volumeRatio: Math.round(volumeRatio * 100) / 100
    };
  } catch (err) {
    console.error("Volume error", symbol, err.message);
    return {
      isSpike: false,
      currentVolume: 0,
      avgVolume: 0,
      maxVolume: 0,
      volumeRatio: 0
    };
  }
}

function chunkArray(arr, size) {
  const chunks = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
}

function connectStreams(streams) {
  const url = `${STREAM_URL}?streams=${streams.join("/")}`;
  const ws = new WebSocket(url);

  ws.on("open", () => console.log(`Connected ${streams.length} pairs`));

  ws.on("message", async (msg) => {
    const json = JSON.parse(msg);
    const k = json.data.k;

    if (k) {
      const symbol = json.data.s;
      const open = parseFloat(k.o);
      const close = parseFloat(k.c);
      const change = ((close - open) / open) * 100;

      if (change >= THRESHOLD) {
        // ✅ check volume spike and get RSI
        const [volumeData, rsi] = await Promise.all([
          checkVolumeSpike(symbol),
          getRSI(symbol)
        ]);

        io.emit("green", {
          symbol,
          change: change.toFixed(2),
          spike: volumeData.isSpike,
          rsi,
          volume: parseFloat(k.v), // Current volume from stream
          price: close.toFixed(8),
          volumeData: {
            current: volumeData.currentVolume,
            average: volumeData.avgVolume,
            ratio: volumeData.volumeRatio,
            max: volumeData.maxVolume
          }
        });
      } else {
        io.emit("red", { symbol });
      }
    }
  });

  ws.on("close", () => {
    console.log("Closed. Reconnecting...");
    setTimeout(() => connectStreams(streams), 3000);
  });
}

(async () => {
  const symbols = await getSymbols();
  console.log(`Tracking ${symbols.length} USDT pairs`);
  const chunks = chunkArray(symbols, 200);
  chunks.forEach(connectStreams);

  // config send karna
  io.on("connection", (socket) => {
    socket.emit("config", {
      interval: INTERVAL,
      threshold: THRESHOLD,
      volumeMultiplier: VOLUME_MULTIPLIER,
    });
  });

  server.listen(3000, () => console.log("http://localhost:3000"));
})();
