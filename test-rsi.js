const axios = require('axios');

// RSI calculation function (same as in server.js)
function calculateRSI(prices, period = 14) {
  if (prices.length < period + 1) return null;
  
  const gains = [];
  const losses = [];
  
  // Calculate price changes
  for (let i = 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }
  
  // Calculate initial average gain and loss
  let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;
  
  // Calculate RSI using <PERSON>'s smoothing method
  for (let i = period; i < gains.length; i++) {
    avgGain = (avgGain * (period - 1) + gains[i]) / period;
    avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
  }
  
  if (avgLoss === 0) return 100;
  const rs = avgGain / avgLoss;
  const rsi = 100 - (100 / (1 + rs));
  
  return Math.round(rsi * 100) / 100;
}

// Get RSI for a symbol
async function getRSI(symbol, interval = '5m') {
  try {
    const url = `https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=${interval}&limit=34`;
    const res = await axios.get(url);
    const candles = res.data;
    
    // Extract closing prices
    const closePrices = candles.map(candle => parseFloat(candle[4]));
    
    return calculateRSI(closePrices);
  } catch (err) {
    console.error("RSI error", symbol, err.message);
    return null;
  }
}

// Test RSI calculation for popular coins
async function testRSI() {
  const testSymbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'];
  
  console.log('Testing RSI calculation for 5m interval:');
  console.log('Symbol\t\tOur RSI\tBinance RSI (manual check needed)');
  console.log('='.repeat(60));
  
  for (const symbol of testSymbols) {
    const rsi = await getRSI(symbol);
    console.log(`${symbol}\t${rsi !== null ? rsi.toFixed(2) : 'N/A'}\t(Check on Binance)`);
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\nTo verify accuracy:');
  console.log('1. Go to https://www.binance.com/en/trade/BTC_USDT');
  console.log('2. Set timeframe to 5m');
  console.log('3. Add RSI indicator');
  console.log('4. Compare the values shown above with Binance RSI');
}

testRSI().catch(console.error);
