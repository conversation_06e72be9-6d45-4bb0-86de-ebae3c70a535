<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Realtime Green Coins</title>
  <script src="/socket.io/socket.io.js"></script>
  <style>
    body { background:#111; color:#fff; font-family:Arial; padding:20px; }
    h1 { margin-bottom:10px; }
    .subtitle { margin-bottom:20px; color:#aaa; }
    .coins-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    .coin-card {
      background: #1a1a1a;
      border: 1px solid #333;
      border-radius: 8px;
      padding: 12px 16px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      min-width: 200px;
    }
    .coin-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.5);
    }
    .coin-card a { color:#ffffff; font-weight:bold; text-decoration:none; font-size: 1.1em; }
    .change { font-weight:bold; }
    .green { color:#00ff00; }
    .spike {
      color: orange;
      font-size: 0.85em;
      font-weight: bold;
      background: rgba(255, 165, 0, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid rgba(255, 165, 0, 0.3);
    }
    .rsi {
      font-size: 0.9em;
      font-weight: bold;
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid;
    }
    .rsi.oversold {
      color: #ff4444;
      background: rgba(255, 68, 68, 0.1);
      border-color: rgba(255, 68, 68, 0.3);
    }
    .rsi.overbought {
      color: #44ff44;
      background: rgba(68, 255, 68, 0.1);
      border-color: rgba(68, 255, 68, 0.3);
    }
    .rsi.neutral {
      color: #ffaa44;
      background: rgba(255, 170, 68, 0.1);
      border-color: rgba(255, 170, 68, 0.3);
    }
    .price {
      color: #aaa;
      font-size: 0.85em;
    }
    .volume-container {
      margin-top: 6px;
    }
    .volume-label {
      font-size: 0.75em;
      color: #888;
      margin-bottom: 2px;
    }
    .volume-bar {
      height: 6px;
      background: #333;
      border-radius: 3px;
      overflow: hidden;
      position: relative;
    }
    .volume-fill {
      height: 100%;
      border-radius: 3px;
      transition: width 0.5s ease;
      position: relative;
    }
    .volume-fill.normal {
      background: linear-gradient(90deg, #4a9eff, #00ff88);
    }
    .volume-fill.spike {
      background: linear-gradient(90deg, #ff6b35, #f7931e);
      animation: pulse 1.5s infinite;
    }
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
    .volume-text {
      font-size: 0.7em;
      color: #aaa;
      margin-top: 2px;
    }
  </style>
</head>
<body>
  <h1>Realtime Green Coins</h1>
  <div id="subtitle" class="subtitle"></div>

  <div class="coins-container" id="coins"></div>

  <script>
    const socket = io();
    const container = document.getElementById("coins");
    const subtitle = document.getElementById("subtitle");
    const cards = {};

    // 🟢 config from server (interval + threshold + volume multiplier)
    socket.on("config", (data) => {
      subtitle.textContent = `Interval: ${data.interval} | Threshold: ${data.threshold}% | Volume Spike: ${data.volumeMultiplier}x`;
    });

    function getRSIClass(rsi) {
      if (rsi <= 30) return 'oversold';
      if (rsi >= 70) return 'overbought';
      return 'neutral';
    }

    function formatVolume(volume) {
      if (volume >= 1e9) return (volume / 1e9).toFixed(1) + 'B';
      if (volume >= 1e6) return (volume / 1e6).toFixed(1) + 'M';
      if (volume >= 1e3) return (volume / 1e3).toFixed(1) + 'K';
      return volume.toFixed(0);
    }

    function addCard(symbol, change, spike, rsi, volume, price, volumeData) {
      const quote = "USDT";
      const base = symbol.replace(quote, "");
      const url = `https://www.binance.com/en/trade/${base}_${quote}`;

      const card = document.createElement("div");
      card.className = "coin-card";

      const link = document.createElement("a");
      link.href = url;
      link.target = "_blank";
      link.textContent = `${base}/${quote}`;

      const changeSpan = document.createElement("span");
      changeSpan.className = "change green";
      changeSpan.textContent = change + "%";

      const priceSpan = document.createElement("span");
      priceSpan.className = "price";
      priceSpan.textContent = `$${price}`;

      card.appendChild(link);
      card.appendChild(changeSpan);
      card.appendChild(priceSpan);

      // RSI display
      if (rsi !== null && rsi !== undefined) {
        const rsiSpan = document.createElement("span");
        rsiSpan.className = `rsi ${getRSIClass(rsi)}`;
        rsiSpan.textContent = `RSI: ${rsi}`;
        card.appendChild(rsiSpan);
      }

      // Volume spike
      if (spike) {
        const spikeTag = document.createElement("span");
        spikeTag.className = "spike";
        spikeTag.textContent = "🔥 Volume Spike!";
        card.appendChild(spikeTag);
      }

      // Volume container
      const volumeContainer = document.createElement("div");
      volumeContainer.className = "volume-container";

      const volumeLabel = document.createElement("div");
      volumeLabel.className = "volume-label";
      volumeLabel.textContent = "Volume";

      const volumeBar = document.createElement("div");
      volumeBar.className = "volume-bar";

      const volumeFill = document.createElement("div");
      volumeFill.className = `volume-fill ${spike ? 'spike' : 'normal'}`;

      const volumeText = document.createElement("div");
      volumeText.className = "volume-text";

      if (volumeData) {
        const percentage = Math.min((volumeData.ratio / 5) * 100, 100); // Scale to max 5x
        volumeFill.style.width = percentage + "%";
        volumeText.textContent = `${formatVolume(volumeData.current)} (${volumeData.ratio}x avg)`;
      } else {
        volumeFill.style.width = "50%";
        volumeText.textContent = formatVolume(volume);
      }

      volumeBar.appendChild(volumeFill);
      volumeContainer.appendChild(volumeLabel);
      volumeContainer.appendChild(volumeBar);
      volumeContainer.appendChild(volumeText);
      card.appendChild(volumeContainer);

      container.appendChild(card);
      cards[symbol] = { card, changeSpan, priceSpan, volumeFill, volumeText };
    }

    function updateCard(symbol, change, spike, rsi, volume, price, volumeData) {
      const { card, changeSpan, priceSpan, volumeFill, volumeText } = cards[symbol];
      changeSpan.textContent = change + "%";
      if (priceSpan) priceSpan.textContent = `$${price}`;

      // Update RSI
      let rsiSpan = card.querySelector(".rsi");
      if (rsi !== null && rsi !== undefined) {
        if (!rsiSpan) {
          rsiSpan = document.createElement("span");
          rsiSpan.className = "rsi";
          card.insertBefore(rsiSpan, card.querySelector(".spike") || card.querySelector(".volume-container"));
        }
        rsiSpan.className = `rsi ${getRSIClass(rsi)}`;
        rsiSpan.textContent = `RSI: ${rsi}`;
      } else if (rsiSpan) {
        rsiSpan.remove();
      }

      // Update volume spike
      let spikeTag = card.querySelector(".spike");
      if (spike && !spikeTag) {
        spikeTag = document.createElement("span");
        spikeTag.className = "spike";
        spikeTag.textContent = "🔥 Volume Spike!";
        card.insertBefore(spikeTag, card.querySelector(".volume-container"));
      } else if (!spike && spikeTag) {
        spikeTag.remove();
      }

      // Update volume bar and text
      if (volumeFill) {
        volumeFill.className = `volume-fill ${spike ? 'spike' : 'normal'}`;

        if (volumeData) {
          const percentage = Math.min((volumeData.ratio / 5) * 100, 100);
          volumeFill.style.width = percentage + "%";
          if (volumeText) {
            volumeText.textContent = `${formatVolume(volumeData.current)} (${volumeData.ratio}x avg)`;
          }
        } else {
          volumeFill.style.width = spike ? "100%" : "50%";
          if (volumeText) {
            volumeText.textContent = formatVolume(volume);
          }
        }
      }
    }

    function removeCard(symbol) {
      if (cards[symbol]) {
        cards[symbol].card.remove();
        delete cards[symbol];
      }
    }

    // add/update green coin
    socket.on("green", (data) => {
      if (!cards[data.symbol]) {
        addCard(data.symbol, data.change, data.spike, data.rsi, data.volume, data.price, data.volumeData);
      } else {
        updateCard(data.symbol, data.change, data.spike, data.rsi, data.volume, data.price, data.volumeData);
      }
    });

    // remove if red
    socket.on("red", (data) => {
      removeCard(data.symbol);
    });
  </script>
</body>
</html>
